<!-- 物价屏 -->
<template>
  <div class="page-container">
    <div class="table-section">
      <div class="table-header">
        <h2>商品价格信息</h2>
      </div>
      <div class="table-wrapper" v-loading="loading">
        <div class="scrolling-table" ref="scrollingTable">
          <el-table :data="priceData" stripe :show-header="true" height="100%" class="price-table">
            <!-- TODO:正式环境需删除  -->
            <el-table-column prop="SerialNo" label="序号" min-width="50" align="center" />
            <el-table-column prop="ItemCode" label="编码" min-width="120" align="center" />
            <el-table-column prop="ItemDesc" label="名称" min-width="200" align="center" />
            <el-table-column prop="SpecInfo" label="规格" min-width="150" align="center" />
            <el-table-column prop="Uom" label="单位" min-width="80" align="center" />
            <el-table-column prop="TarSubCate" label="类型" min-width="120" align="center" />
            <el-table-column prop="Price" label="单价(元)" min-width="120" align="center" />
          </el-table>
        </div>

        <!-- 加载更多提示 -->
        <div v-if="isLoadingMore" class="loading-more-tip">
          <i class="el-icon-loading"></i>
          <span>正在加载更多数据...</span>
        </div>

        <!-- TODO:正式环境需删除  -->
        <!-- 数据统计信息 -->
        <div v-if="!loading && totalCount > 0" class="data-info">
          <span>已显示 {{ priceData.length }} / {{ totalCount }} 条记录</span>
          <span v-if="!hasMoreData" class="no-more-data">（已加载全部数据）</span>
          <!-- 手动加载所有数据按钮 -->
          <el-button
            v-if="hasMoreData && !isLoadingMore"
            @click="loadAllRemainingData"
            size="mini"
            type="primary"
            class="load-all-btn"
          >
            加载全部数据
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { dataConversion } from "@/utils/index.js";

export default {
  name: "priceScreen",
  data() {
    return {
      loading: true,
      scrollTimer: null,
      scrollSpeed: 0.5, // 滚动速度（像素/帧）
      currentScrollTop: 0, // 当前滚动位置

      // 分页相关
      currentPage: 1, // 当前页码
      pageSize: 50, // 每页条数
      totalPages: 0, // 总页数
      totalCount: 0, // 总记录数
      hasMoreData: true, // 是否还有更多数据
      isLoadingMore: false, // 是否正在加载更多数据

      // 无限滚动相关
      originalData: [], // 原始数据
      priceData: [], // 显示的数据
      maxDisplayItems: 200, // 最大显示条数，超过后清理前面的数据
      minDataForScroll: 10, // 最少需要多少条数据才开始滚动
      duplicateMultiplier: 3, // 数据复制倍数

      // 滚动检测相关
      scrollCheckDistance: 200, // 距离底部多少像素时触发加载
      scrollCheckTimer: null, // 滚动检测定时器
    };
  },
  created() {
    this.loadInitialData();
  },

  methods: {
    // 加载初始数据（第一页）
    async loadInitialData() {
      this.loading = true;
      this.currentPage = 1;
      this.priceData = [];

      try {
        const res = await this.$api.price.queryPrice({
          alias: "", // 空值查询全部
          page: this.currentPage,
          pageSize: this.pageSize,
        });

        console.log("物价接口初始数据", res);

        if (res.success && res.data.ResultCode == "0") {
          // 处理返回的数据
          this.handleApiResponse(res, true);

          // 如果有更多数据，自动加载所有数据以确保显示完整
          if (this.hasMoreData) {
            console.log("检测到有更多数据，自动加载所有数据");
            await this.loadAllRemainingData();
          }

          this.initPage();
        } else {
          this.$message.error(res.message || "数据加载失败");
          // API失败时保持空数据状态
          this.handleApiFailure();
        }
      } catch (error) {
        console.error("加载初始数据失败:", error);
        this.$message.error("数据加载异常");
        // API异常时保持空数据状态
        this.handleApiFailure();
      } finally {
        this.loading = false;
      }
    },

    // 加载所有剩余数据
    async loadAllRemainingData() {
      console.log("开始加载所有剩余数据");

      while (this.hasMoreData && !this.isLoadingMore) {
        try {
          await this.loadMoreData();
          // 添加小延迟避免请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.error("加载剩余数据时出错:", error);
          break;
        }
      }

      console.log(`所有数据加载完成，总共${this.priceData.length}条记录`);
    },

    // 加载更多数据（下一页）
    async loadMoreData() {
      if (this.isLoadingMore || !this.hasMoreData) {
        return;
      }

      this.isLoadingMore = true;
      this.currentPage++;

      try {
        const res = await this.$api.price.queryPrice({
          alias: "", // 空值查询全部
          page: this.currentPage,
          pageSize: this.pageSize,
        });

        console.log(`物价接口第${this.currentPage}页数据`, res);

        if (res.success && res.data.ResultCode === "0") {
          // 处理返回的数据
          this.handleApiResponse(res, false);
        } else {
          this.$message.error(res.message || "加载更多数据失败");
          this.currentPage--; // 回退页码
        }
      } catch (error) {
        console.error("加载更多数据失败:", error);
        this.$message.error("加载更多数据异常");
        this.currentPage--; // 回退页码
      } finally {
        this.isLoadingMore = false;
      }
    },

    // 处理API响应数据
    handleApiResponse(res, isInitial = false) {
      const { data, pagination } = res;

      // 更新分页信息
      this.totalPages = pagination.totalPages;
      this.totalCount = pagination.totalCount;
      this.hasMoreData = this.currentPage < this.totalPages;

      // 处理数据项
      let newItems = [];
      if (data.TarItemS && data.TarItemS.TarItem) {
        const items = dataConversion(data.TarItemS.TarItem);

        newItems = items.map(item => ({
          SerialNo: item.SerialNo || "",
          ItemCode: item.ItemCode || "",
          ItemDesc: item.ItemDesc || "",
          SpecInfo: item.SpecInfo || "",
          Uom: item.Uom || "",
          TarSubCate: item.TarSubCate || "",
          Price: item.Price || "",
        }));
      }

      if (isInitial) {
        // 初始加载，替换数据
        this.priceData = newItems;
        this.originalData = [...newItems];
      } else {
        // 追加数据
        this.priceData = [...this.priceData, ...newItems];
        // 更新原始数据以包含所有已加载的真实数据
        this.originalData = [...this.priceData];
      }

      console.log(
        `数据处理完成，当前共${this.priceData.length}条记录，总共${this.totalCount}条，还有${
          this.hasMoreData ? "更多" : "没有更多"
        }数据`
      );
    },

    // 处理API失败情况
    handleApiFailure() {
      // 重置为空数据状态
      this.priceData = [];
      this.originalData = [];
      this.totalCount = 0;
      this.totalPages = 0;
      this.hasMoreData = false;
      this.currentPage = 1;
      console.log("API调用失败，数据列表已清空");
    },

    // 初始化页面
    async initPage() {
      this.$nextTick(() => {
        // 延迟一点时间确保表格完全渲染
        setTimeout(() => {
          this.setupScrollDetection();
          this.checkAndStartScrolling();
        }, 500);
      });
    },

    // 设置滚动检测
    setupScrollDetection() {
      const tableWrapper = this.$refs.scrollingTable;
      if (!tableWrapper) {
        console.log("滚动表格的Ref未找到");
        return;
      }

      const tableBody = tableWrapper.querySelector(".el-table__body-wrapper");
      if (!tableBody) {
        console.log("表格主体包装器tableBody未找到");
        return;
      }

      // 添加滚动事件监听
      tableBody.addEventListener("scroll", this.handleScroll);
      console.log("滚动检测已设置");
    },

    // 处理滚动事件
    handleScroll(event) {
      // 使用防抖，避免频繁触发
      if (this.scrollCheckTimer) {
        clearTimeout(this.scrollCheckTimer);
      }

      this.scrollCheckTimer = setTimeout(() => {
        this.checkScrollPosition(event.target);
      }, 100);
    },

    // 检查滚动位置，判断是否需要加载更多数据
    checkScrollPosition(scrollElement) {
      if (!scrollElement || this.isLoadingMore || !this.hasMoreData) {
        return;
      }

      const scrollTop = scrollElement.scrollTop;
      const scrollHeight = scrollElement.scrollHeight;
      const clientHeight = scrollElement.clientHeight;

      // 计算距离底部的距离
      const distanceToBottom = scrollHeight - scrollTop - clientHeight;

      // 如果距离底部小于设定值，触发加载更多
      if (distanceToBottom <= this.scrollCheckDistance) {
        console.log(`距离底部${distanceToBottom}px，触发加载更多数据`);
        this.loadMoreData();
      }
    },

    // 检查是否需要滚动并开始滚动
    checkAndStartScrolling() {
      const tableWrapper = this.$refs.scrollingTable;
      if (!tableWrapper) {
        console.log("滚动表格的Ref未找到");
        return;
      }

      this.$nextTick(() => {
        const tableBody = tableWrapper.querySelector(".el-table__body-wrapper");
        if (!tableBody) {
          console.log("表格主体包装器tableBody未找到");
          return;
        }

        // 检查是否需要滚动（内容高度大于容器高度）
        const scrollHeight = tableBody.scrollHeight;
        const clientHeight = tableBody.clientHeight;

        console.log(`scrollHeight: ${scrollHeight}, clientHeight: ${clientHeight}`);

        if (scrollHeight > clientHeight) {
          console.log("内容需要滚动,开始滚动动画");
          this.startScrolling();
        } else {
          console.log("内容完全适配容器,无需滚动操作");
          // 如果数据不够滚动且还有更多数据，优先加载更多真实数据
          if (this.hasMoreData) {
            console.log("数据量不足以滚动且还有更多数据，加载更多真实数据");
            this.loadMoreData().then(() => {
              // 加载完成后重新检查
              setTimeout(() => {
                this.checkAndStartScrolling();
              }, 500);
            });
          } else if (this.priceData.length < this.minDataForScroll) {
            console.log("没有更多真实数据且数据量太少，复制数据以支持滚动");
            this.duplicateDataForScrolling();
          }
        }
      });
    },

    // 复制数据以确保有足够的内容进行滚动
    duplicateDataForScrolling() {
      console.log("复制数据以支持滚动");

      // 如果没有更多数据且当前数据不足，复制现有数据多次
      if (!this.hasMoreData && this.priceData.length > 0) {
        const currentData = [...this.priceData];
        const targetLength = Math.max(
          this.minDataForScroll,
          currentData.length * this.duplicateMultiplier
        );
        const newData = [];

        while (newData.length < targetLength) {
          newData.push(...currentData);
        }

        this.priceData = newData.slice(0, targetLength);

        console.log(`数据已复制，总共${this.priceData.length}条记录`);

        this.$nextTick(() => {
          this.startScrolling();
        });
      }
    },

    // 开始滚动动画
    startScrolling() {
      const tableWrapper = this.$refs.scrollingTable;
      if (!tableWrapper) {
        console.log("滚动表格的Ref未找到");
        return;
      }

      // 等待DOM渲染完成后再开始滚动
      this.$nextTick(() => {
        // 查找表格的body容器
        const tableBody = tableWrapper.querySelector(".el-table__body-wrapper");
        if (!tableBody) {
          console.log("表格主体包装器tableBody未找到");
          return;
        }

        console.log("开始滚动动画");
        const scroll = () => {
          this.currentScrollTop += this.scrollSpeed;

          // 获取表格的实际高度
          const scrollHeight = tableBody.scrollHeight;
          const clientHeight = tableBody.clientHeight;
          const maxScroll = scrollHeight - clientHeight;

          // 当滚动接近底部时，优先加载真实数据而不是复制数据
          if (this.currentScrollTop >= maxScroll - 100) {
            if (this.hasMoreData && !this.isLoadingMore) {
              console.log("接近底部，加载更多真实数据");
              this.loadMoreData().then(() => {
                // 加载完成后继续滚动
                setTimeout(() => {
                  this.startScrolling();
                }, 300);
              });
              return;
            } else if (!this.hasMoreData && this.priceData.length < this.maxDisplayItems) {
              // 只有在没有更多真实数据时才复制数据
              this.appendDataForInfiniteScroll();
              return;
            }
          }

          // 如果到达底部，重置到顶部继续滚动
          if (this.currentScrollTop >= maxScroll) {
            this.currentScrollTop = 0;
            console.log("到达底部，重置滚动位置到顶部");
          }

          tableBody.scrollTop = this.currentScrollTop;
          this.scrollTimer = requestAnimationFrame(scroll);
        };

        this.scrollTimer = requestAnimationFrame(scroll);
      });
    },

    // 为无限滚动追加数据（仅在没有更多真实数据时使用）
    appendDataForInfiniteScroll() {
      console.log(`当前显示数据量: ${this.priceData.length}`);

      // 只有在没有更多真实数据时才复制数据
      if (this.hasMoreData) {
        console.log("还有更多真实数据，不进行数据复制");
        return;
      }

      // 检查是否有原始数据可用于复制
      if (this.originalData.length === 0) {
        console.log("没有原始数据可用于滚动复制");
        return;
      }

      // 检查是否需要清理数据以控制内存使用
      if (this.priceData.length >= this.maxDisplayItems) {
        console.log("数据量过多，正在清理前面的数据");
        this.cleanupExcessData();
        return;
      }

      // 在数组尾部追加原始数据，避免重新计算滚动位置
      this.priceData = [...this.priceData, ...this.originalData];

      console.log(`数据已追加，当前总数据量: ${this.priceData.length}`);

      // 继续滚动
      this.$nextTick(() => {
        this.startScrolling();
      });
    },

    // 清理过多的数据以控制内存使用
    cleanupExcessData() {
      console.log("清理过多的数据");

      // 检查是否有原始数据可用于重置
      if (this.originalData.length === 0) {
        console.log("没有原始数据可用于重置");
        return;
      }

      // 保留最后一部分数据，然后重新开始
      const keepItems = this.originalData.length * this.duplicateMultiplier;
      this.priceData = this.priceData.slice(-keepItems);

      // 重置滚动位置到开始
      this.currentScrollTop = 0;

      console.log(`数据已清理，保留${this.priceData.length}条记录`);

      this.$nextTick(() => {
        const tableWrapper = this.$refs.scrollingTable;
        const tableBody = tableWrapper.querySelector(".el-table__body-wrapper");
        if (tableBody) {
          tableBody.scrollTop = 0;
        }
        this.startScrolling();
      });
    },

    // 清除滚动定时器
    clearScrollTimer() {
      if (this.scrollTimer) {
        cancelAnimationFrame(this.scrollTimer);
        this.scrollTimer = null;
      }
    },

    // 视频错误处理
    handleVideoError() {
      this.videoError = true;
      console.error("视频加载失败");
    },

    // 视频开始加载
    handleVideoLoadStart() {
      this.videoError = false;
    },

    // 视频可以播放
    handleVideoCanPlay() {
      this.videoError = false;
    },
  },
  beforeDestroy() {
    this.clearScrollTimer();

    // 清理滚动检测定时器
    if (this.scrollCheckTimer) {
      clearTimeout(this.scrollCheckTimer);
      this.scrollCheckTimer = null;
    }

    // 移除滚动事件监听
    const tableWrapper = this.$refs.scrollingTable;
    if (tableWrapper) {
      const tableBody = tableWrapper.querySelector(".el-table__body-wrapper");
      if (tableBody) {
        tableBody.removeEventListener("scroll", this.handleScroll);
      }
    }
  },
};
</script>

<style lang="scss" scoped>
$leftWidth: 100%;
// $rightWidth: 40%;

.page-container {
  display: flex;
  background: #f5f5f5;
  overflow: hidden;

  .table-section {
    width: $leftWidth;
    height: 100%;
    display: flex;
    flex-direction: column;

    .table-header {
      margin-bottom: 15px;
      text-align: center;

      h2 {
        color: #333;
        font-size: 24px;
        font-weight: bold;
        margin: 0;
      }
    }

    .table-wrapper {
      flex: 1;
      background: #ffffff;
      border: 1px solid #e0e0e0;
      overflow: hidden;
      position: relative;

      .loading-more-tip {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        z-index: 1000;

        i {
          margin-right: 8px;
        }
      }

      .data-info {
        position: absolute;
        bottom: 5px;
        right: 10px;
        font-size: 12px;
        color: #666;
        background: rgba(255, 255, 255, 0.9);
        padding: 4px 8px;
        border-radius: 4px;
        z-index: 999;

        .no-more-data {
          color: #999;
          margin-left: 8px;
        }

        .load-all-btn {
          margin-left: 12px;
          font-size: 11px;
          padding: 4px 8px;
        }
      }

      .scrolling-table {
        height: 100%;
        overflow: hidden;

        // 隐藏滚动条
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */

        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera */
        }

        .price-table {
          width: 100%;
          height: 100%;

          // 表格头部样式
          /deep/ .el-table__header-wrapper {
            .el-table__header {
              th {
                background: #409eff;
                color: #ffffff;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                border-bottom: 1px solid #ddd;

                .cell {
                  padding: 12px 8px;
                }
              }
            }
          }

          // 表格主体样式
          /deep/ .el-table__body-wrapper {
            // 隐藏滚动条
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */

            &::-webkit-scrollbar {
              display: none; /* Chrome, Safari, Opera */
            }

            .el-table__body {
              tr {
                td {
                  border-bottom: 1px solid #f0f0f0;
                  font-size: 13px;
                  color: #333;

                  .cell {
                    padding: 10px 8px;
                    text-align: center;
                  }

                  .price-text {
                    font-weight: bold;
                    color: #e74c3c;
                    font-size: 14px;
                  }
                }

                // 斑马纹样式
                &.el-table__row--striped {
                  td {
                    background-color: #fafafa;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// Loading 样式覆盖
::v-deep .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);

  .el-loading-spinner {
    .el-loading-text {
      color: #409eff;
      font-weight: bold;
    }

    .circular {
      color: #409eff;
    }
  }
}
</style>
